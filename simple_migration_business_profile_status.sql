-- Simple Database Migration: Update Business Profile Status Constraint
-- Date: 2025-07-10
-- Purpose: Add missing official Twilio Trust Hub Customer Profile status values
-- Issue: BusinessProfileStatus enum validation rejecting "in-review" status

-- This is a simplified version that just updates the constraint

BEGIN;

-- Drop the existing constraint
ALTER TABLE public.trust_hub_customer_profiles 
DROP CONSTRAINT IF EXISTS chk_status_valid_values;

-- Add the updated constraint with all official Twilio status values
ALTER TABLE public.trust_hub_customer_profiles 
ADD CONSTRAINT chk_status_valid_values CHECK (
  (
    (status IS NULL)
    OR (
      (status)::text = ANY (
        ARRAY[
          -- Official Twilio Trust Hub Customer Profile status values
          ('draft'::character varying)::text,
          ('pending-review'::character varying)::text,
          ('in-review'::character varying)::text,
          ('twilio-approved'::character varying)::text,
          ('twilio-rejected'::character varying)::text,
          ('provisionally-approved'::character varying)::text,
          -- Legacy values for backward compatibility
          ('submitted'::character varying)::text,
          ('approved'::character varying)::text,
          ('rejected'::character varying)::text
        ]
      )
    )
  )
);

-- Verify the constraint was added successfully
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conname = 'chk_status_valid_values' 
  AND conrelid = 'public.trust_hub_customer_profiles'::regclass;

COMMIT;

-- Show current status distribution (optional - run separately if needed)
/*
SELECT 
  status,
  COUNT(*) as count
FROM public.trust_hub_customer_profiles 
WHERE status IS NOT NULL
GROUP BY status
ORDER BY count DESC;
*/
