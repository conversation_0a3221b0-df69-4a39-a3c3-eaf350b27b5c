"use client"

import { useState, useEffect, useMemo, use, type ChangeEvent } from "react"
import { HeaderDock } from "@/components/header-dock"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Calendar, ChevronLeft, ChevronRight, Phone, Edit, Trophy, XCircle, UserX, CheckCircle, Sparkles, Shield, Loader2, StopCircle, AlertTriangle, X } from "lucide-react"
import { type Appointment, type EnhancedAppointment, type Lead } from "@/lib/types"
import { ConnectCalendarModal } from "@/components/connect-calendar-modal"
import { CalendarConnectionPopup } from "@/components/calendar/calendar-connection-popup"
import { extractPhoneFromTitle } from "@/lib/utils/phone-extraction"
import { useConversationalHistory, useAutoRefreshStatus } from "@/hooks/use-conversational-history"
import { useManualRefresh } from "@/hooks/use-manual-refresh"
import { useRefreshShortcut } from "@/hooks/use-keyboard-shortcuts"
import { RefreshStatusIndicator } from "@/components/ui/refresh-status-indicator"
import { ConversationalDataAgeIndicator } from "@/components/conversation/conversational-data-age-indicator"
import { useUpdateLeadStatus, useUpdateLeadNotes, useUpdateBookingStatus, useLeadExtraDetails } from "@/hooks/use-lead-data"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { WeeklyView } from "@/components/weekly-view"
import { MonthlyView } from "@/components/monthly-view"
import { CalendarNavigation } from "@/components/calendar-navigation"
import { CalendarRefreshControls } from "@/components/calendar/calendar-refresh-controls"
import { MobileCalendarNav, MobileFloatingActionButton } from "@/components/mobile/mobile-calendar-nav"
import { PerformanceMonitor } from "@/components/performance-monitor"

import { useCalendarEvents } from "@/hooks/use-calendar-events"
import { useCalendarConnection } from "@/hooks/use-calendar-connection"
import { useClientUrlParams } from "@/hooks/use-client-url-params"
import { GlowingButton } from "@/components/ui/glowing-button"
import { ConnectionStatusTooltip } from "@/components/calendar/connection-status-indicators"
import { useCalendarKeyboardNavigation } from "@/hooks/use-keyboard-shortcuts"
import { useIsMobile } from "@/hooks/use-mobile"
import { getDailyDateRange, isSameDay } from "@/lib/utils/calendar-utils"
import { AdvancedCalendarDashboard } from "@/components/calendar/advanced-calendar-dashboard"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"

type ViewMode = "daily" | "weekly" | "monthly"
type LeadStatus = "won" | "lost" | "no-show" | "active"

// EnhancedAppointment type is now imported from @/lib/types

interface HomePageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }> | { [key: string]: string | string[] | undefined }
}

export default function HomePage({ searchParams }: HomePageProps) {
  const isMobile = useIsMobile()

  // Handle calendar connection feedback from URL parameters
  // Use React.use() to unwrap searchParams if it's a Promise (Next.js 15)
  const resolvedSearchParams = searchParams instanceof Promise ? use(searchParams) : searchParams
  const calendarConnectionCancelled = (resolvedSearchParams?.calendar_connection_cancelled as string) === 'true'
  const calendarConnected = (resolvedSearchParams?.calendar_connected as string) === 'true'
  const adminAccessDenied = (resolvedSearchParams?.error as string) === 'admin_access_denied'

  // Use client-side URL parameter handling (prevents SSR hydration issues)
  const {
    isInitialized: isUrlParamsInitialized,
    viewMode: urlViewMode,
    selectedDate: urlSelectedDate,
    hadUrlParams
  } = useClientUrlParams()

  // Local state for view mode and date (hydration-safe)
  const [viewMode, setViewMode] = useState<"daily" | "weekly" | "monthly">("daily")
  const [selectedDate, setSelectedDate] = useState(new Date('2025-06-24T00:00:00.000Z')) // Stable date for SSR
  const [isInitializing, setIsInitializing] = useState(true)
  const [isHydrated, setIsHydrated] = useState(false)

  // Handle hydration and update to current date
  useEffect(() => {
    setIsHydrated(true)

    // If no URL parameters were applied, update to current date after hydration
    if (isUrlParamsInitialized && !hadUrlParams) {
      setSelectedDate(new Date())
    }
  }, [isUrlParamsInitialized, hadUrlParams])

  // Apply URL parameters after client-side initialization
  useEffect(() => {
    if (isUrlParamsInitialized) {
      setViewMode(urlViewMode)
      if (hadUrlParams) {
        setSelectedDate(urlSelectedDate)
        console.log('Applied URL parameters:', { viewMode: urlViewMode, date: urlSelectedDate })
      }
      setIsInitializing(false)
    }
  }, [isUrlParamsInitialized, urlViewMode, urlSelectedDate, hadUrlParams])

  const [selectedAppointment, setSelectedAppointment] = useState<string | null>(null)
  const [pendingAppointmentSelection, setPendingAppointmentSelection] = useState<string | null>(null)
  const [showCalendarModal, setShowCalendarModal] = useState(false)
  const [showAdvancedFeatures, setShowAdvancedFeatures] = useState(false)
  const [selectedPhoneNumber, setSelectedPhoneNumber] = useState<string | null>(null)
  const [selectedAppointmentTitle, setSelectedAppointmentTitle] = useState<string | null>(null)
  const [newNote, setNewNote] = useState("")
  const [savedNotes, setSavedNotes] = useState<Array<{ id: string; content: string; timestamp: Date }>>([])  // Remove hardcoded notes - use API data instead
  const [isStopDialogOpen, setIsStopDialogOpen] = useState(false)
  const [showExtraDetailsModal, setShowExtraDetailsModal] = useState(false)
  const [notesValue, setNotesValue] = useState("")
  const [lastSavedNotes, setLastSavedNotes] = useState("")
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null)

  // API mutation hooks for real data integration
  const updateStatusMutation = useUpdateLeadStatus()
  const updateNotesMutation = useUpdateLeadNotes()
  const updateBookingStatusMutation = useUpdateBookingStatus()

  // Extra details hook
  const { data: extraDetails, isLoading: extraDetailsLoading } = useLeadExtraDetails(selectedPhoneNumber)

  // Calendar connection status
  const {
    connectionStatus,
    shouldShowPopup,
    connect,
    dismissPopup,
    isConnecting,
    isLoading: connectionLoading,
    error: connectionError
  } = useCalendarConnection()

  // Conversational history for selected phone number with auto-refresh and optimization
  const {
    data: conversationalData,
    isLoading: conversationalLoading,
    error: conversationalError,
    refetch: refreshConversationalHistory,
    lastFetchTime: conversationalLastFetchTime
  } = useConversationalHistory(selectedPhoneNumber, {
    enableAutoRefresh: true,
    autoRefreshInterval: 5 * 60 * 1000, // 5 minutes
    useOptimizedEndpoint: true, // Use optimized conversation-only endpoint
    selectiveFields: ['conversation', 'status'] // Only fetch essential fields
  })

  // Auto-refresh status for UI indicators
  const autoRefreshStatus = useAutoRefreshStatus(selectedPhoneNumber)

  // Enhanced manual refresh functionality
  const {
    refresh: manualRefresh,
    isRefreshing: isManualRefreshing,
    lastRefreshTime,
    refreshError
  } = useManualRefresh(selectedPhoneNumber)

  // Keyboard shortcut for refresh (Ctrl+R/Cmd+R)
  useRefreshShortcut(manualRefresh, true)

  // Handle calendar connection feedback and admin access errors
  useEffect(() => {
    if (calendarConnectionCancelled) {
      // User cancelled the OAuth flow - no need for error message, just clear the URL
      // The connection status will remain disconnected, which is correct
      console.log('Calendar connection was cancelled by user')

      // Clean up the URL parameter without showing an error
      // This provides a clean UX where the user simply returns to the home page
      const url = new URL(window.location.href)
      url.searchParams.delete('calendar_connection_cancelled')
      window.history.replaceState({}, '', url.toString())
    }

    if (calendarConnected) {
      // User successfully connected calendar
      console.log('Calendar connection successful')

      // Clean up the URL parameter
      const url = new URL(window.location.href)
      url.searchParams.delete('calendar_connected')
      window.history.replaceState({}, '', url.toString())

      // The connection status will be updated by the useCalendarConnection hook
      // No additional action needed as the UI will automatically reflect the connected state
    }

    if (adminAccessDenied) {
      // Clean up the URL parameter after showing the error
      const url = new URL(window.location.href)
      url.searchParams.delete('error')
      window.history.replaceState({}, '', url.toString())
    }
  }, [calendarConnectionCancelled, calendarConnected, adminAccessDenied])

  // Use React Query-powered calendar events hook for daily view
  const { startDate: dailyStartDate, endDate: dailyEndDate } = useMemo(() =>
    getDailyDateRange(selectedDate), [selectedDate]
  )

  const {
    events,
    isLoading: eventsLoading,
    error: eventsError,
    isFetching,
    forceRefresh,
    prefetchAdjacentRanges,
    metadata
  } = useCalendarEvents({
    viewMode: 'daily',
    startDate: dailyStartDate,
    endDate: dailyEndDate,
    enabled: viewMode === 'daily'
  })

  // Prefetch adjacent days for smooth navigation
  useEffect(() => {
    if (viewMode === 'daily' && !eventsLoading) {
      prefetchAdjacentRanges()
    }
  }, [viewMode, selectedDate, eventsLoading, prefetchAdjacentRanges])

  // Transform calendar events to appointment format for daily view
  const todaysAppointments = useMemo((): EnhancedAppointment[] => {
    if (viewMode !== "daily") {
      // For weekly/monthly views, return empty array - no mock data fallbacks
      return []
    }

    return events
      .filter(event => isSameDay(new Date(event.startTime), selectedDate))
      .map(event => ({
        id: event.id,
        leadId: event.id, // Use event ID as lead ID for mapping
        date: selectedDate.toISOString().split("T")[0] || '',
        time: new Date(event.startTime).toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit'
        }),
        endTime: new Date(event.endTime).toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit'
        }),
        duration: Math.round((new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60)), // duration in minutes
        status: event.status === 'confirmed' ? 'scheduled' : event.status === 'cancelled' ? 'cancelled' : 'scheduled',
        outcome: event.status === 'confirmed' ? 'today' : event.status === 'cancelled' ? 'lost' : undefined,
        // Map event data to lead-like structure
        eventData: {
          title: event.title,
          description: event.description,
          location: event.location,
          attendees: event.attendees || [],
          isAllDay: event.isAllDay,
          status: event.status
        }
      }))
  }, [events, selectedDate, viewMode])

  // Automatically select appointment when navigating from Week view
  useEffect(() => {
    if (viewMode === 'daily' && pendingAppointmentSelection && !eventsLoading && todaysAppointments.length > 0) {
      // Find the appointment that matches the pending selection
      const appointmentToSelect = todaysAppointments.find(apt => apt.id === pendingAppointmentSelection)

      if (appointmentToSelect) {
        // Automatically select the appointment
        setSelectedAppointment(pendingAppointmentSelection)

        // Extract phone number from appointment title (same logic as handleAppointmentClick)
        if (appointmentToSelect.eventData?.title) {
          const phoneNumber = extractPhoneFromTitle(appointmentToSelect.eventData.title)
          if (phoneNumber) {
            setSelectedPhoneNumber(phoneNumber)
            setSelectedAppointmentTitle(appointmentToSelect.eventData.title)
            console.log('Auto-extracted phone number from Week view:', phoneNumber)
          } else {
            // Clear phone number if no phone found in title
            setSelectedPhoneNumber(null)
            setSelectedAppointmentTitle(appointmentToSelect.eventData.title)
          }
        } else {
          // Clear phone number if no appointment data
          setSelectedPhoneNumber(null)
          setSelectedAppointmentTitle(null)
        }

        // Clear the pending selection
        setPendingAppointmentSelection(null)

        console.log('Auto-selected appointment from Week view:', appointmentToSelect.eventData?.title)
      } else {
        // If appointment not found, clear pending selection
        console.warn('Pending appointment not found in Day view:', pendingAppointmentSelection)
        setPendingAppointmentSelection(null)
      }
    }
  }, [viewMode, pendingAppointmentSelection, eventsLoading, todaysAppointments])

  // Create lead data from selected appointment/event
  const selectedLead = useMemo(() => {
    if (!selectedAppointment) return null

    if (viewMode === "daily") {
      const appointment = todaysAppointments.find(apt => apt.id === selectedAppointment)
      if (!appointment?.eventData) return null

      // Use real lead data if available, otherwise fall back to event data
      if (conversationalData?.hasData && conversationalData.leadData) {
        const leadData = conversationalData.leadData
        return {
          id: appointment.id,
          name: leadData.first_name && leadData.last_name
            ? `${leadData.first_name} ${leadData.last_name}`
            : leadData.first_name || appointment.eventData.title || 'Calendar Event',
          email: leadData.email || appointment.eventData.attendees?.[0] || '<EMAIL>',
          phone: leadData.phone_number || selectedPhoneNumber || '(*************',
          address: appointment.eventData.location || 'No location specified',
          tags: [
            ...(leadData.primary_tags ? leadData.primary_tags.split(',').map(tag => tag.trim()) : []),
            ...(leadData.sub_tags ? leadData.sub_tags.split(',').map(tag => tag.trim()) : [])
          ].filter(Boolean),
          leadStatus: leadData.lead_status,
          notes: leadData.notes
        }
      } else {
        // Transform event data to lead format (fallback)
        const eventData = appointment.eventData
        const attendeeEmail = eventData.attendees?.[0] || '<EMAIL>'

        return {
          id: appointment.id,
          name: eventData.title || 'Calendar Event',
          email: attendeeEmail,
          phone: selectedPhoneNumber || '(*************',
          address: eventData.location || 'No location specified',
          tags: [
            eventData.isAllDay ? 'All Day' : 'Scheduled',
            eventData.status === 'confirmed' ? 'Confirmed' :
            eventData.status === 'tentative' ? 'Tentative' : 'Cancelled'
          ].filter(Boolean)
        }
      }
    } else {
      // For weekly/monthly views, we don't have real lead data integration yet
      // Return null to show appropriate "no data" state
      return null
    }
  }, [selectedAppointment, todaysAppointments, viewMode, conversationalData, selectedPhoneNumber])

  // Sync notes value with selected lead's notes
  useEffect(() => {
    if (selectedLead?.notes) {
      setNotesValue(selectedLead.notes)
      setLastSavedNotes(selectedLead.notes)
    } else {
      setNotesValue("")
      setLastSavedNotes("")
    }
  }, [selectedLead])

  // Use real conversation messages from API
  const leadMessages = useMemo(() => {
    if (viewMode === "daily" && conversationalData?.hasData && conversationalData.messages) {
      // Transform real conversation messages to match expected format
      return conversationalData.messages.map((msg, index) => ({
        id: `real-${index}`,
        leadId: selectedLead?.id || '',
        content: msg.message,
        timestamp: msg.timestamp || new Date().toISOString(),
        sender: msg.sender === 'Bot' ? 'tara' : 'lead'
      }))
    }
    // Return empty array if no real data available - no mock fallbacks
    return []
  }, [selectedLead, conversationalData, viewMode])

  const getStatusIcon = (outcome?: string) => {
    switch (outcome) {
      case "won":
        return (
          <span className="w-5 h-5 flex items-center justify-center rounded-full bg-green-500 text-white text-xs font-bold">
            W
          </span>
        )
      case "lost":
        return (
          <span className="w-5 h-5 flex items-center justify-center rounded-full bg-red-500 text-white text-xs font-bold">
            L
          </span>
        )
      case "no-show":
        return (
          <span className="w-5 h-5 flex items-center justify-center rounded-full bg-yellow-500 text-white text-xs font-bold">
            S
          </span>
        )
      case "today":
        return (
          <span className="w-5 h-5 flex items-center justify-center rounded-full bg-blue-500 text-white text-xs font-bold">
            T
          </span>
        )
      default:
        return null
    }
  }

  const formatTime = (time: string | undefined) => {
    if (!time) return ''
    const [hours, minutes] = time.split(":")
    const hour = Number.parseInt(hours || '0')
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes || '00'} ${ampm}`
  }

  const handleStatusChange = async (status: LeadStatus) => {
    if (!selectedLead || !selectedPhoneNumber) {
      console.warn('No selected lead or phone number for status update')
      return
    }

    // Map status values to API format
    const apiStatus = status === "won" ? "Won" : status === "lost" ? "Lost" : "No Show"

    // Prevent unnecessary API calls if status is already current
    if (selectedLead.leadStatus === apiStatus) {
      console.log(`Lead status is already ${apiStatus}, skipping API call`)
      return
    }

    try {
      await updateStatusMutation.mutateAsync({
        leadId: parseInt(selectedLead.id), // Keep for compatibility but use phoneNumber
        status: apiStatus as "Won" | "Lost" | "No Show",
        phoneNumber: selectedPhoneNumber
      })
    } catch (error) {
      console.error('Failed to update lead status:', error)
    }
  }

  const handleSaveNote = () => {
    if (newNote.trim()) {
      const note = {
        id: Date.now().toString(),
        content: newNote.trim(),
        timestamp: new Date(),
      }
      setSavedNotes([note, ...savedNotes])
      setNewNote("")
    }
  }

  // Auto-save notes functionality
  const handleNotesChange = (value: string) => {
    setNotesValue(value)

    // Clear existing timeout
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
    }

    // Set new timeout for auto-save (1 second debounce)
    const timeout = setTimeout(() => {
      if (value !== lastSavedNotes && selectedLead && selectedPhoneNumber) {
        saveNotesToAPI(value)
      }
    }, 1000)

    setAutoSaveTimeout(timeout)
  }

  const saveNotesToAPI = async (notes: string) => {
    if (!selectedLead || !selectedPhoneNumber) return

    try {
      await updateNotesMutation.mutateAsync({
        leadId: parseInt(selectedLead.id),
        notes: notes,
        phoneNumber: selectedPhoneNumber
      })
      setLastSavedNotes(notes)
    } catch (error) {
      console.error('Failed to save notes:', error)
    }
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout)
      }
    }
  }, [autoSaveTimeout])

  // Stop AI Communication handler
  const handleStopCommunication = async () => {
    if (!selectedLead || !selectedPhoneNumber) {
      console.warn('No selected lead or phone number for stopping communication')
      return
    }

    try {
      await updateBookingStatusMutation.mutateAsync({
        leadId: parseInt(selectedLead.id),
        bookingStatus: false, // Stop AI communication
        phoneNumber: selectedPhoneNumber
      })
      setIsStopDialogOpen(false)
    } catch (error) {
      console.error('Failed to stop AI communication:', error)
    }
  }



  const getTagColor = (tag: string) => {
    switch (tag.toLowerCase()) {
      case "medicare":
        return "bg-blue-100 text-blue-800"
      case "senior":
        return "bg-purple-100 text-purple-800"
      case "family":
        return "bg-yellow-100 text-yellow-800"
      case "self-employed":
        return "bg-green-100 text-green-800"
      case "individual":
        return "bg-pink-100 text-pink-800"
      case "young adult":
        return "bg-indigo-100 text-indigo-800"
      case "high priority":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }





  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    // Clear selected appointment when changing dates
    setSelectedAppointment(null)
  }

  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode)
    // Clear selected appointment when changing view modes
    setSelectedAppointment(null)
    // Clear pending appointment selection if switching away from daily view
    if (mode !== 'daily') {
      setPendingAppointmentSelection(null)
    }
  }

  // Combined handler for "Today" button navigation
  const handleTodayNavigate = () => {
    handleDateSelect(new Date())
    handleViewModeChange("daily")
  }

  // Handle appointment click to show lead details in sidebar
  const handleAppointmentClick = (appointmentId: string) => {
    setSelectedAppointment(appointmentId)
  }

  // Handle appointment selection from WeeklyView navigation
  const handleAppointmentSelectFromWeekView = (appointmentId: string) => {
    // Store the appointment ID to be selected when Day view loads
    setPendingAppointmentSelection(appointmentId)

    // Find the appointment and extract phone number from title
    const appointment = todaysAppointments.find(apt => apt.id === appointmentId)
    if (appointment?.eventData?.title) {
      const phoneNumber = extractPhoneFromTitle(appointment.eventData.title)
      if (phoneNumber) {
        setSelectedPhoneNumber(phoneNumber)
        setSelectedAppointmentTitle(appointment.eventData.title)
        // Note: No modal - data will be displayed in the sidebar
      } else {
        // Clear phone number if no phone found in title
        setSelectedPhoneNumber(null)
        setSelectedAppointmentTitle(appointment.eventData.title)
      }
    } else {
      // Clear phone number if no appointment data
      setSelectedPhoneNumber(null)
      setSelectedAppointmentTitle(null)
    }
  }

  // Keyboard navigation support
  useCalendarKeyboardNavigation({
    onNavigateDate: (direction) => {
      const newDate = new Date(selectedDate)
      switch (viewMode) {
        case 'daily':
          newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))
          break
        case 'weekly':
          newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
          break
        case 'monthly':
          newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1))
          break
      }
      handleDateSelect(newDate)
    },
    onViewModeChange: handleViewModeChange,
    onCreateEvent: () => {
      // Handle create event - could open EventModal
      console.log('Create event shortcut pressed')
    },
    onToday: handleTodayNavigate,
    onAdvancedFeatures: () => setShowAdvancedFeatures(true),
    enabled: !isMobile // Disable on mobile to avoid conflicts with touch
  })

  // Navigation is now handled by CalendarNavigation component

  return (
    <div className="min-h-screen bg-gray-50">
      <HeaderDock />

      <main className={cn(
        isMobile ? "pt-0" : "pt-16"
      )}>
        {/* Admin Access Denied Alert */}
        {adminAccessDenied && (
          <div className="mx-6 mt-4">
            <Alert variant="destructive">
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <strong>Access Denied:</strong> You don't have permission to access the admin area.
                Admin privileges are required for that section.
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Mobile Navigation */}
        {isMobile ? (
          <MobileCalendarNav
            currentDate={selectedDate}
            onDateChange={handleDateSelect}
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onCreateEvent={() => {
              console.log('Create event from mobile nav')
            }}
            onSearch={() => setShowAdvancedFeatures(true)}
            onAdvancedFeatures={() => setShowAdvancedFeatures(true)}
            onTodayNavigate={handleTodayNavigate}
          />
        ) : (
          /* Desktop Calendar Header with Centralized Navigation */
          <div className="bg-white border-b border-gray-200">
            <div className="px-6 py-4 flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">Calendar</h1>

              <div className="flex items-center space-x-4">
                {isMobile ? (
                  <Sheet open={showAdvancedFeatures} onOpenChange={setShowAdvancedFeatures}>
                    <SheetTrigger asChild>
                      <Button
                        variant="outline"
                        className="flex items-center space-x-2"
                      >
                        <Sparkles className="w-4 h-4" />
                        <span>Advanced</span>
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="h-[90vh]">
                      <SheetHeader>
                        <SheetTitle>Advanced Calendar Features</SheetTitle>
                      </SheetHeader>
                      <div className="mt-4 overflow-y-auto h-[calc(90vh-80px)]">
                        <AdvancedCalendarDashboard
                          selectedDate={selectedDate}
                          onEventSelect={(event) => {
                            // Handle event selection from search results
                            console.log('Selected event from advanced features:', event)
                            setShowAdvancedFeatures(false)
                          }}
                          onTemplateSelect={(eventData) => {
                            // Handle template selection
                            console.log('Selected template:', eventData)
                            setShowAdvancedFeatures(false)
                          }}
                        />
                      </div>
                    </SheetContent>
                  </Sheet>
                ) : (
                  <Dialog open={showAdvancedFeatures} onOpenChange={setShowAdvancedFeatures}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="flex items-center space-x-2"
                      >
                        <Sparkles className="w-4 h-4" />
                        <span>Advanced Features</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Advanced Calendar Features</DialogTitle>
                      </DialogHeader>
                      <AdvancedCalendarDashboard
                        selectedDate={selectedDate}
                        onEventSelect={(event) => {
                          // Handle event selection from search results
                          console.log('Selected event from advanced features:', event)
                          setShowAdvancedFeatures(false)
                        }}
                        onTemplateSelect={(eventData) => {
                          // Handle template selection
                          console.log('Selected template:', eventData)
                          setShowAdvancedFeatures(false)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                )}

                <ConnectionStatusTooltip
                  connectionStatus={connectionStatus}
                  isLoading={connectionLoading}
                  error={connectionError}
                >
                  <GlowingButton
                    variant="outline"
                    onClick={() => setShowCalendarModal(true)}
                    className="flex items-center space-x-2"
                    isGlowing={!connectionStatus?.connected}
                  >
                    <Calendar className="w-4 h-4" />
                    <span>{connectionStatus?.connected ? 'Calendar Settings' : 'Connect Calendar'}</span>
                  </GlowingButton>
                </ConnectionStatusTooltip>



                {/* Status Legend and Refresh Controls for Daily View */}
                {viewMode === "daily" && (
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-6">
                    {/* Status Legend */}
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span>Won (W)</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <span>Lost (L)</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <span>No Show (S)</span>
                      </div>
                    </div>

                    {/* Refresh Controls */}
                    <CalendarRefreshControls
                      metadata={metadata}
                      onRefresh={forceRefresh}
                      isRefreshing={isFetching}
                      viewMode="daily"
                      showSeparateRefreshButton={!isMobile}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Centralized Calendar Navigation */}
            <CalendarNavigation
              currentDate={selectedDate}
              onDateChange={handleDateSelect}
              viewMode={viewMode}
              onViewModeChange={handleViewModeChange}
              onTodayNavigate={handleTodayNavigate}
            />
          </div>
        )}

        {/* URL Parameters Applied Client-Side */}
        {hadUrlParams && (
          <div className="bg-green-50 border-l-4 border-green-400 p-4 mx-6 mt-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700">
                  <strong>URL Parameters Applied:</strong> Calendar view and date have been set from the URL.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Main Content - Daily View */}
        {viewMode === "daily" && (
          <div className={cn(
            isMobile
              ? "flex flex-col h-[calc(100vh-140px)]"
              : "grid grid-cols-12 gap-6 p-6 h-[calc(100vh-200px)]"
          )}>
            {/* Left Sidebar - Today's Appointments */}
            <div className={cn(
              isMobile ? "flex-1 min-h-0" : "col-span-3"
            )}>
              <Card className={cn(
                isMobile ? "h-full mx-2 mb-2" : "h-full"
              )}>
                <CardHeader className={cn(
                  isMobile ? "pb-2 px-3 pt-3" : ""
                )}>
                  <CardTitle className={cn(
                    isMobile ? "text-base" : "text-lg"
                  )}>
                    {isHydrated && selectedDate.toDateString() === new Date().toDateString()
                      ? "Today's Appointments"
                      : `Appointments for ${selectedDate.toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}`}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  {/* Loading State */}
                  {eventsLoading && (
                    <div className="p-6 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span className="text-gray-600">Loading events...</span>
                      </div>
                    </div>
                  )}

                  {/* Background Fetching Indicator */}
                  {!eventsLoading && isFetching && (
                    <div className="p-2 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                        <span className="text-gray-500 text-sm">Refreshing...</span>
                      </div>
                    </div>
                  )}

                  {/* Calendar Connection Prompt */}
                  {!connectionStatus?.connected && !eventsLoading && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg m-4">
                      <div className="flex items-center space-x-3">
                        <Calendar className="w-5 h-5 text-blue-600" />
                        <div className="flex-1">
                          <div className="text-blue-800 font-medium text-sm">
                            Connect your Google Calendar
                          </div>
                          <div className="text-blue-700 text-sm mt-1">
                            Connect your calendar to see your real appointments and events here.
                          </div>
                        </div>
                        <Button
                          onClick={connect}
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Connect
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Error State (for actual errors, not connection issues) */}
                  {eventsError && connectionStatus?.connected && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg m-4">
                      <div className="text-red-700 text-sm">
                        Error loading calendar events: {eventsError.message}
                      </div>
                    </div>
                  )}

                  {/* Appointments List */}
                  {!eventsLoading && (!eventsError || !connectionStatus?.connected) && (
                    <ScrollArea className={cn(
                      isMobile ? "h-[calc(100vh-200px)]" : "h-[calc(100vh-300px)]"
                    )}>
                      <div className="space-y-0">
                        {todaysAppointments.length > 0 ? (
                          todaysAppointments.map((appointment) => {
                            const isSelected = selectedAppointment === appointment.id
                            const eventData = appointment.eventData

                            return (
                              <div
                                key={appointment.id}
                                className={cn(
                                  "cursor-pointer transition-colors hover:bg-gray-50 border-l-4",
                                  isMobile ? "p-3" : "p-4",
                                  isSelected ? "border-blue-500 bg-blue-50" : "border-transparent"
                                )}
                                onClick={() => handleAppointmentClick(appointment.id)}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <h3 className={cn(
                                      "font-semibold text-gray-900",
                                      isMobile ? "text-base" : "text-lg"
                                    )}>
                                      {eventData?.title || 'Calendar Event'}
                                    </h3>
                                    <div className={cn(
                                      "text-gray-600 mt-1",
                                      isMobile ? "text-xs" : "text-sm"
                                    )}>
                                      {eventData?.isAllDay ? (
                                        <span>All Day</span>
                                      ) : (
                                        <span>
                                          {formatTime(appointment.time)} - {formatTime(appointment.endTime)}
                                        </span>
                                      )}
                                    </div>
                                    {eventData?.location && (
                                      <div className="flex items-center space-x-1 text-gray-600 text-sm mt-2">
                                        <span>📍</span>
                                        <span>{eventData.location}</span>
                                      </div>
                                    )}
                                    {eventData?.attendees && eventData.attendees.length > 0 && (
                                      <div className="flex items-center space-x-1 text-gray-600 text-sm mt-1">
                                        <Phone className="w-3 h-3" />
                                        <span>{eventData.attendees[0]}</span>
                                      </div>
                                    )}
                                    <div className="flex flex-wrap gap-2 mt-3">
                                      {eventData?.status && (
                                        <Badge
                                          variant="secondary"
                                          className={`text-xs ${
                                            eventData.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                            eventData.status === 'tentative' ? 'bg-yellow-100 text-yellow-800' :
                                            eventData.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                            'bg-gray-100 text-gray-800'
                                          }`}
                                        >
                                          {eventData.status}
                                        </Badge>
                                      )}
                                      {eventData?.isAllDay && (
                                        <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                                          All Day
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  <div className="ml-2">{getStatusIcon(appointment.outcome)}</div>
                                </div>
                              </div>
                            )
                          })
                        ) : (
                          <div className="p-6 text-center text-gray-500">
                            {viewMode === "daily" ? "No calendar events for this day" : "No appointments for this day"}
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Center Panel - Communication - Hidden on mobile */}
            {!isMobile && (
              <div className="col-span-5">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">
                        {selectedLead ? selectedLead.name : "Select an appointment"}
                      </CardTitle>
                      {selectedLead && <div className="text-gray-600 text-sm">{selectedLead.email}</div>}
                    </div>
                    {/* Enhanced refresh status indicator */}
                    {selectedPhoneNumber && (
                      <div className="flex items-center gap-2">
                        <ConversationalDataAgeIndicator
                          lastFetchTime={conversationalLastFetchTime}
                          isRefreshing={conversationalLoading}
                          messageCount={leadMessages.length}
                          onRefresh={manualRefresh}
                          cacheHit={false} // Conversational data is always fresh from database
                        />
                        <RefreshStatusIndicator
                          lastRefreshTime={lastRefreshTime}
                          nextRefreshTime={autoRefreshStatus.nextRefreshTime}
                          isAutoRefreshActive={autoRefreshStatus.isAutoRefreshActive}
                          isRefreshing={isManualRefreshing || conversationalLoading}
                          onManualRefresh={manualRefresh}
                          refreshError={refreshError}
                          compact={true}
                        />
                      </div>
                    )}
                  </div>
                </CardHeader>

                {selectedLead ? (
                  <div className="flex-1 flex flex-col">
                    <ScrollArea className="flex-1 p-4">
                      <div className="space-y-4">


                        {/* Loading State for Conversation */}
                        {conversationalLoading && selectedPhoneNumber && (
                          <div className="flex items-center justify-center py-4">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                            <span className="ml-2 text-gray-500 text-sm">Loading conversation...</span>
                          </div>
                        )}

                        {/* Error State for Conversation */}
                        {conversationalError && selectedPhoneNumber && (
                          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div className="text-yellow-700 text-sm">
                              Failed to load conversation history
                            </div>
                          </div>
                        )}

                        {/* No Lead Found State */}
                        {!conversationalLoading && !conversationalError && conversationalData?.noLeadFound && selectedPhoneNumber && (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div className="text-blue-700 text-sm">
                              No lead record found for this phone number
                            </div>
                          </div>
                        )}

                        {/* Real Conversation Messages */}
                        {leadMessages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${message.sender === "tara" ? "justify-end" : "justify-start"}`}
                          >
                            <div
                              className={`max-w-[80%] rounded-lg p-3 ${
                                message.sender === "tara"
                                  ? "bg-green-500 text-white rounded-tr-none"
                                  : "bg-gray-100 text-gray-900 rounded-tl-none"
                              }`}
                            >
                              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                              <p
                                className={`text-xs mt-1 ${message.sender === "tara" ? "opacity-70 text-right" : "text-gray-600"}`}
                              >
                                {(() => {
                                  const messageDate = new Date(message.timestamp);
                                  const today = new Date();
                                  const isToday = messageDate.toDateString() === today.toDateString();

                                  if (isToday) {
                                    return messageDate.toLocaleTimeString([], {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    });
                                  } else {
                                    return `${messageDate.toLocaleDateString()} ${messageDate.toLocaleTimeString([], {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}`;
                                  }
                                })()}
                              </p>
                            </div>
                          </div>
                        ))}

                        {/* No Messages State */}
                        {!conversationalLoading && !conversationalError && !conversationalData?.noLeadFound && leadMessages.length === 0 && selectedPhoneNumber && (
                          <div className="text-center py-4 text-gray-500">
                            No conversation history found for this lead
                          </div>
                        )}
                      </div>
                    </ScrollArea>


                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    Select an appointment to view communication history
                  </div>
                )}
              </Card>
              </div>
            )}

            {/* Right Sidebar - Lead Status & Profile */}
            {!isMobile && (
              <div className="col-span-4 space-y-6">
              {/* Lead Status Classification */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Lead Status</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {selectedLead ? (
                    <div className="grid grid-cols-3 gap-3">
                      <Button
                        onClick={() => handleStatusChange("won")}
                        disabled={updateStatusMutation.isPending}
                        className={`flex flex-col items-center p-3 h-auto border-2 ${
                          selectedLead.leadStatus === 'Won'
                            ? 'border-green-500 bg-green-100 text-green-700'
                            : 'border-gray-300 hover:border-green-500 hover:bg-green-50 hover:text-green-600 text-gray-600'
                        }`}
                        variant="outline"
                      >
                        {updateStatusMutation.isPending && updateStatusMutation.variables?.status === 'Won' ? (
                          <Loader2 className="w-5 h-5 mb-2 animate-spin" />
                        ) : (
                          <Trophy className="w-5 h-5 mb-2" />
                        )}
                        <span className="text-sm font-medium">Won</span>
                      </Button>
                      <Button
                        onClick={() => handleStatusChange("lost")}
                        disabled={updateStatusMutation.isPending}
                        className={`flex flex-col items-center p-3 h-auto border-2 ${
                          selectedLead.leadStatus === 'Lost'
                            ? 'border-red-500 bg-red-100 text-red-700'
                            : 'border-gray-300 hover:border-red-500 hover:bg-red-50 hover:text-red-600 text-gray-600'
                        }`}
                        variant="outline"
                      >
                        {updateStatusMutation.isPending && updateStatusMutation.variables?.status === 'Lost' ? (
                          <Loader2 className="w-5 h-5 mb-2 animate-spin" />
                        ) : (
                          <XCircle className="w-5 h-5 mb-2" />
                        )}
                        <span className="text-sm font-medium">Lost</span>
                      </Button>
                      <Button
                        onClick={() => handleStatusChange("no-show")}
                        disabled={updateStatusMutation.isPending}
                        className={`flex flex-col items-center p-3 h-auto border-2 ${
                          selectedLead.leadStatus === 'No Show'
                            ? 'border-yellow-500 bg-yellow-100 text-yellow-700'
                            : 'border-gray-300 hover:border-yellow-500 hover:bg-yellow-50 hover:text-yellow-600 text-gray-600'
                        }`}
                        variant="outline"
                      >
                        {updateStatusMutation.isPending && updateStatusMutation.variables?.status === 'No Show' ? (
                          <Loader2 className="w-5 h-5 mb-2 animate-spin" />
                        ) : (
                          <UserX className="w-5 h-5 mb-2" />
                        )}
                        <span className="text-sm font-medium">No Show</span>
                      </Button>
                    </div>
                  ) : (
                    <div className="text-gray-500 text-center py-8">Select an appointment to update lead status</div>
                  )}
                </CardContent>
              </Card>

              {/* AI Communication Control */}
              {selectedLead && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">AI Communication</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <AlertDialog open={isStopDialogOpen} onOpenChange={setIsStopDialogOpen}>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          className="w-full flex items-center gap-2"
                          disabled={updateBookingStatusMutation.isPending}
                        >
                          {updateBookingStatusMutation.isPending ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <StopCircle className="w-4 h-4" />
                          )}
                          Stop AI Communication
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="w-5 h-5 text-red-500" />
                            Stop AI Communication
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently stop all AI communication for this lead. This action cannot be undone.
                            <br /><br />
                            <strong>Lead:</strong> {selectedLead.name}
                            <br />
                            <strong>Phone:</strong> {selectedPhoneNumber}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleStopCommunication}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Stop Communication
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </CardContent>
                </Card>
              )}

              {/* Lead Profile */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-lg">Lead Profile</CardTitle>
                  <div className="flex space-x-2">
                    {selectedPhoneNumber && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
                        onClick={() => setShowExtraDetailsModal(true)}
                      >
                        Extra Details
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  {conversationalLoading && selectedPhoneNumber ? (
                    <div className="space-y-4">
                      {/* Loading skeleton */}
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ) : selectedLead ? (
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-gray-600 text-sm mb-1">Full Name</h3>
                        <p className="text-gray-900">{selectedLead.name}</p>
                      </div>

                      <div>
                        <h3 className="text-gray-600 text-sm mb-1">Email</h3>
                        <p className="text-gray-900">{selectedLead.email}</p>
                      </div>

                      <div>
                        <h3 className="text-gray-600 text-sm mb-1">Phone</h3>
                        <p className="text-gray-900">{selectedLead.phone}</p>
                      </div>

                      <div>
                        <h3 className="text-gray-600 text-sm mb-1">Address</h3>
                        <p className="text-gray-900">{selectedLead.address}</p>
                      </div>

                      <div>
                        <h3 className="text-gray-600 text-sm mb-1">Tags</h3>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {selectedLead.tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className={`text-xs ${getTagColor(tag)}`}>
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Show lead status if available */}
                      {selectedLead.leadStatus && (
                        <div>
                          <h3 className="text-gray-600 text-sm mb-1">Lead Status</h3>
                          <div className="flex items-center">
                            <div className={`w-3 h-3 rounded-full mr-2 ${
                              selectedLead.leadStatus === 'Won' ? 'bg-green-500' :
                              selectedLead.leadStatus === 'Lost' ? 'bg-red-500' :
                              selectedLead.leadStatus === 'No Show' ? 'bg-yellow-500' :
                              'bg-blue-500'
                            }`}></div>
                            <span className="text-gray-900">{selectedLead.leadStatus}</span>
                          </div>
                        </div>
                      )}

                      {/* Show error if API call failed but we have appointment data */}
                      {conversationalError && selectedPhoneNumber && (
                        <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
                          Using appointment data - lead details unavailable
                        </div>
                      )}

                      {/* Show info message for no lead found */}
                      {!conversationalLoading && !conversationalError && conversationalData?.noLeadFound && selectedPhoneNumber && (
                        <div className="p-2 bg-blue-50 border border-blue-200 rounded text-blue-700 text-sm">
                          No lead record found - showing appointment data only
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-center py-8">Select an appointment to view lead profile</div>
                  )}
                </CardContent>
              </Card>

              {/* Notes */}
              <Card className="flex-1">
                <CardHeader>
                  <CardTitle className="text-lg">Notes</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {selectedLead ? (
                    <div className="space-y-4">
                      <div>
                        <div className="relative">
                          <Textarea
                            placeholder="Add notes about this lead..."
                            value={notesValue}
                            onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleNotesChange(e.target.value)}
                            className="min-h-[120px] resize-none"
                            maxLength={1000}
                          />

                          {/* Save Status Indicator */}
                          <div className="absolute top-2 right-2">
                            {updateNotesMutation.isPending && (
                              <div className="flex items-center gap-1 text-xs text-blue-600">
                                <Loader2 className="h-3 w-3 animate-spin" />
                                <span>Saving...</span>
                              </div>
                            )}
                            {updateNotesMutation.isSuccess && !updateNotesMutation.isPending && notesValue === lastSavedNotes && (
                              <div className="flex items-center gap-1 text-xs text-green-600">
                                <CheckCircle className="h-3 w-3" />
                                <span>Saved</span>
                              </div>
                            )}
                            {notesValue !== lastSavedNotes && !updateNotesMutation.isPending && (
                              <div className="flex items-center gap-1 text-xs text-orange-600">
                                <Edit className="h-3 w-3" />
                                <span>Unsaved</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Character Count and Status */}
                        <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center gap-2">
                            <span className={notesValue.length > 800 ? "text-orange-600" : ""}>
                              {notesValue.length}/1000 characters
                            </span>
                            {updateNotesMutation.isError && (
                              <span className="text-red-600">Failed to save</span>
                            )}
                          </div>
                          <span>Auto-saves after 1 second</span>
                        </div>
                      </div>

                      {/* Additional Notes Section for Local Notes */}
                      {savedNotes.length > 0 && (
                        <div className="border-t pt-4 max-h-[calc(100vh-640px)] overflow-y-auto">
                          <h4 className="font-medium text-gray-900 mb-3">Additional Notes</h4>
                          {savedNotes.map((note) => (
                            <div key={note.id} className="mb-4 p-3 bg-gray-50 rounded-lg">
                              <div className="flex justify-between items-start">
                                <h3 className="font-medium text-gray-900 text-sm">
                                  {isHydrated && note.timestamp.toDateString() === new Date().toDateString()
                                    ? "Meeting Notes"
                                    : "Previous Note"}
                                </h3>
                                <span className="text-xs text-gray-600">
                                  {isHydrated && note.timestamp.toDateString() === new Date().toDateString()
                                    ? `Today, ${note.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`
                                    : note.timestamp.toLocaleDateString()}
                                </span>
                              </div>
                              <p className="text-gray-600 mt-1 text-sm">{note.content}</p>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-center py-8">Select an appointment to view notes</div>
                  )}
                </CardContent>
              </Card>
              </div>
            )}
          </div>
        )}

        {/* Weekly View */}
        {viewMode === "weekly" && (
          <WeeklyView
            selectedDate={selectedDate}
            onDateSelect={handleDateSelect}
            onViewModeChange={handleViewModeChange}
            onAppointmentSelect={handleAppointmentSelectFromWeekView}
            className={isMobile ? "px-2" : ""}
          />
        )}

        {/* Monthly View */}
        {viewMode === "monthly" && (
          <MonthlyView
            selectedDate={selectedDate}
            onDateSelect={handleDateSelect}
            onViewModeChange={handleViewModeChange}
            onAppointmentSelect={handleAppointmentSelectFromWeekView}
            className={isMobile ? "px-2" : ""}
          />
        )}
      </main>

      {/* Mobile Floating Action Button */}
      {isMobile && (
        <MobileFloatingActionButton
          onCreateEvent={() => {
            console.log('Create event from FAB')
            // TODO: Open EventModal for creating new event
          }}
        />
      )}

      <ConnectCalendarModal open={showCalendarModal} onOpenChange={setShowCalendarModal} />

      {/* Calendar Connection Popup */}
      <CalendarConnectionPopup
        isOpen={shouldShowPopup}
        onConnect={connect}
        onDismiss={dismissPopup}
        isConnecting={isConnecting}
      />



      {/* Extra Details Modal */}
      {showExtraDetailsModal && selectedPhoneNumber && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full flex flex-col max-h-[70vh] shadow-xl">
            {/* Fixed Header */}
            <div className="flex justify-between items-center p-6 pb-4 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-xl font-semibold">Extra Lead Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowExtraDetailsModal(false)}
                className="hover:bg-gray-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-hidden">
              {(() => {
                // Calculate populated fields to determine if scrolling is needed
                const populatedFields = extraDetails?.data ?
                  Object.entries(extraDetails.data).filter(([key, value]) =>
                    value !== null && value !== undefined && value !== ''
                  ) : [];

                const shouldScroll = populatedFields.length >= 5;

                return (
                  <div className={`p-6 pt-4 h-full ${shouldScroll ? 'overflow-y-auto' : ''} relative`} style={shouldScroll ? {
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#d1d5db #f3f4f6'
                  } : {}}>
                    {/* Scroll fade indicator at top */}
                    {shouldScroll && (
                      <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white to-transparent pointer-events-none z-10"></div>
                    )}

                    {extraDetailsLoading ? (
                      <div className="space-y-4">
                        <div className="animate-pulse">
                          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-4/5"></div>
                        </div>
                      </div>
                    ) : extraDetails?.data ? (
                      <div className="space-y-4">
                        {populatedFields.map(([key, value]) => {
                          // Format field names to be more readable
                          const formatFieldName = (fieldName: string) => {
                            return fieldName
                              .replace(/_/g, ' ')
                              .replace(/\b\w/g, l => l.toUpperCase())
                              .replace(/Aca/g, 'ACA')
                              .replace(/Cobra/g, 'COBRA');
                          };

                          // Format values
                          const formatValue = (val: any) => {
                            if (typeof val === 'boolean') {
                              return val ? 'Yes' : 'No';
                            }
                            return String(val);
                          };

                          return (
                            <div key={key} className="border-b border-gray-200 pb-3 last:border-b-0">
                              <div className="text-sm font-medium text-gray-600 mb-1">
                                {formatFieldName(key)}
                              </div>
                              <div className="text-gray-900">
                                {formatValue(value)}
                              </div>
                            </div>
                          );
                        })}

                        {populatedFields.length === 0 && (
                          <div className="text-center text-gray-500 py-8">
                            No additional details available for this lead.
                          </div>
                        )}

                        {/* Scroll indicator for many fields */}
                        {shouldScroll && (
                          <div className="text-center text-xs text-gray-400 py-2 border-t border-gray-100">
                            {populatedFields.length} fields • Scroll to view all
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        Failed to load extra details.
                      </div>
                    )}

                    {/* Scroll fade indicator at bottom */}
                    {shouldScroll && (
                      <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white to-transparent pointer-events-none z-10"></div>
                    )}
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Performance Monitor (Development Only) */}
      <PerformanceMonitor />


    </div>
  )
}
