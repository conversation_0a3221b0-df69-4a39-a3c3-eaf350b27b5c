create table public.trust_hub_customer_profiles (
  subaccount_sid character varying(34) not null,
  friendly_name character varying(255) null,
  email character varying(255) null,
  status character varying(50) null,
  status_callback character varying(500) null,
  business_name character varying(255) null,
  business_type character varying(100) null,
  business_registration_number character varying(100) null,
  website_url character varying(500) null,
  business_identity character varying(50) null,
  business_industry character varying(50) null,
  business_registration_identifier character varying(20) null,
  business_regions_of_operation character varying(50) null,
  business_regions text[] null,
  social_media_profile_urls text null,
  policy_sid character varying(34) null,
  other_registration_type_name character varying(100) null,
  business_end_user_sid character varying(34) null,
  authorized_rep_end_user_sid character varying(34) null,
  address_sid character varying(34) null,
  address_supporting_document_sid character varying(34) null,
  primary_customer_profile_sid character varying(34) null,
  rep_first_name character varying(100) null,
  rep_last_name character varying(100) null,
  rep_email character varying(255) null,
  rep_phone character varying(20) null,
  rep_job_position character varying(100) null,
  rep_business_title character varying(100) null,
  street character varying(255) null,
  street_secondary character varying(255) null,
  city character varying(100) null,
  state_province character varying(50) null,
  postal_code character varying(20) null,
  country character varying(3) null,
  submitted_for_review_at timestamp with time zone null,
  approved_at timestamp with time zone null,
  rejected_at timestamp with time zone null,
  rejection_reason text null,
  evaluation_status character varying(50) null,
  evaluation_results jsonb null,
  evaluation_sid character varying(34) null,
  submission_date timestamp with time zone null,
  approval_date timestamp with time zone null,
  rejection_date timestamp with time zone null,
  profile_type character varying(20) null default 'primary'::character varying,
  subaccount_account_sid character varying(34) null,
  status_callback_url text null,
  valid_until timestamp with time zone null,
  errors jsonb null,
  metadata jsonb null default '{}'::jsonb,
  created_by uuid null,
  is_active boolean null default true,
  date_created timestamp with time zone null,
  date_updated timestamp with time zone null,
  updated_at timestamp with time zone null default now(),
  business_registration_id character varying(255) null,
  business_registration_id_type character varying(100) null,
  street_address character varying(255) null,
  representative_first_name character varying(100) null,
  representative_last_name character varying(100) null,
  representative_email character varying(255) null,
  representative_phone character varying(50) null,
  representative_job_title character varying(100) null,
  business_enduser_sid character varying(34) null,
  auth_rep_enduser_sid character varying(34) null,
  supporting_document_sid character varying(34) null,
  bundle_sid character varying(34) null,
  customer_profile_sid character varying(34) not null,
  constraint trust_hub_customer_profiles_pkey primary key (customer_profile_sid),
  constraint trust_hub_customer_profiles_bundle_sid_key unique (bundle_sid),
  constraint trust_hub_customer_profiles_created_by_fkey foreign KEY (created_by) references auth.users (id) on delete set null,
  constraint trust_hub_customer_profiles_subaccount_sid_fkey foreign KEY (subaccount_sid) references twilio_subaccounts (subaccount_sid) on delete CASCADE,
  constraint trust_hub_customer_profiles_business_registration_identif_check check (
    (
      (business_registration_identifier)::text = any (
        array[
          ('EIN'::character varying)::text,
          ('US_EIN'::character varying)::text,
          ('DUNS'::character varying)::text,
          ('CCN'::character varying)::text,
          ('CBN'::character varying)::text,
          ('CN'::character varying)::text,
          ('ACN'::character varying)::text,
          ('AU_ABN'::character varying)::text,
          ('CIN'::character varying)::text,
          ('VAT'::character varying)::text,
          ('VATRN'::character varying)::text,
          ('RN'::character varying)::text,
          ('Other'::character varying)::text
        ]
      )
    )
  ),
  constraint chk_customer_profile_sid_format check (
    (
      (customer_profile_sid)::text ~ '^BU[a-f0-9]{32}$'::text
    )
  ),
  constraint trust_hub_customer_profiles_business_registration_id_type_check check (
    (
      (business_registration_id_type is null)
      or (
        (business_registration_id_type)::text = any (
          array[
            ('US_EIN'::character varying)::text,
            ('AU_ABN'::character varying)::text,
            ('EIN'::character varying)::text,
            ('DUNS'::character varying)::text,
            ('CCN'::character varying)::text,
            ('CBN'::character varying)::text,
            ('CN'::character varying)::text,
            ('ACN'::character varying)::text,
            ('CIN'::character varying)::text,
            ('VAT'::character varying)::text,
            ('VATRN'::character varying)::text,
            ('RN'::character varying)::text,
            ('Other'::character varying)::text
          ]
        )
      )
    )
  ),
  constraint chk_bundle_sid_format check (
    (
      (bundle_sid is null)
      or ((bundle_sid)::text ~ '^BU[a-f0-9]{32}$'::text)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_trust_hub_customer_profiles_subaccount on public.trust_hub_customer_profiles using btree (subaccount_sid) TABLESPACE pg_default;

create index IF not exists idx_trust_hub_customer_profiles_bundle_sid on public.trust_hub_customer_profiles using btree (bundle_sid) TABLESPACE pg_default
where
  (bundle_sid is not null);

create index IF not exists idx_trust_hub_customer_profiles_status_type on public.trust_hub_customer_profiles using btree (status, profile_type) TABLESPACE pg_default;

create index IF not exists idx_trust_hub_customer_profiles_created_by on public.trust_hub_customer_profiles using btree (created_by) TABLESPACE pg_default
where
  (created_by is not null);

create index IF not exists idx_trust_hub_customer_profiles_type on public.trust_hub_customer_profiles using btree (profile_type) TABLESPACE pg_default;

create index IF not exists idx_trust_hub_customer_profiles_webhook_lookup on public.trust_hub_customer_profiles using btree (customer_profile_sid, status, profile_type) TABLESPACE pg_default;