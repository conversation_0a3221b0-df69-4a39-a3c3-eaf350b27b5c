# NeuroV CRM - Comprehensive Caching Architecture

## Overview

The NeuroV CRM implements a sophisticated **4-layer hybrid caching architecture** designed to provide cross-session data persistence, cross-device synchronization, and optimal performance while maintaining security for sensitive user data. The system includes specialized implementations for calendar events, analytics data, and battlegrounds workflow state persistence.

## Architecture Layers

### 1. Memory Layer (TanStack React Query)
- **Purpose**: In-memory caching for active application state
- **Technology**: TanStack React Query v5
- **Configuration**: 1-hour stale time (standardized), 30-minute garbage collection
- **Features**: Smart refresh system, automatic background refresh, intelligent prefetching

### 2. Browser Persistence Layer (IndexedDB/localStorage)
- **Purpose**: Cross-session data persistence
- **Primary**: IndexedDB for structured data storage
- **Fallback**: localStorage for compatibility
- **Features**: Automatic fallback, quota management, data versioning

### 3. Server Cache Layer (Redis)
- **Purpose**: Cross-device synchronization and cache invalidation
- **Technology**: Redis with TTL-based expiration
- **Features**: Real-time invalidation signals, user-specific cache keys

### 4. CDN Layer (Future Enhancement)
- **Purpose**: Global performance optimization for static assets
- **Status**: Planned for future implementation

## Implementation Details

### Smart Calendar Refresh System

The NeuroV CRM implements an intelligent refresh strategy that standardizes all auto-refresh intervals to 1 hour while adding smart session-based refresh detection for calendar events.

#### Key Features
- **Standardized Intervals**: All data types now use 1-hour auto-refresh (83% reduction in API calls)
- **Smart Session Detection**: Automatically refreshes when user returns after >1 hour absence
- **Cross-Session Persistence**: Tracks refresh timestamps across browser sessions and devices
- **SSR-Safe Implementation**: Proper client-side detection for localStorage access

#### Smart Refresh Decision Matrix
```typescript
// Refresh Logic
if (timeSinceLastFetch > 1_HOUR) {
  triggerImmediateRefresh() // >1 hour since last fetch
} else if (isNewSession && timeSinceLastFetch > 30_MINUTES) {
  triggerImmediateRefresh() // New session + >30 minutes
} else {
  scheduleNextAutoRefresh(1_HOUR - timeSinceLastFetch)
}
```

#### Session Timestamp Management
```typescript
interface CalendarRefreshTimestamps {
  lastFetch: string // ISO timestamp
  lastAutoRefresh: string // ISO timestamp
  sessionId: string // Unique session identifier
  deviceId: string // Cross-device tracking
  version: number // Schema versioning
}
```

### Enhanced Query Client

```typescript
// Enhanced cache-aware query hook with smart refresh
const { data, isLoading, error } = useCacheAwareQuery(
  ['calendar-events', viewMode, startDate, endDate],
  () => fetchCalendarEvents(startDate, endDate, viewMode),
  {
    staleTime: 60 * 60 * 1000, // 1 hour (standardized)
    persist: true, // Enable cross-session persistence
    sensitive: false, // Calendar events are not sensitive
    priority: 'high', // High priority for calendar data
    refetchInterval: 60 * 60 * 1000 // 1 hour auto-refresh
  }
)
```

### Persistence Rules

| Data Type | Persist in Production | Persist in Development | Priority | TTL | Auto-Refresh |
|-----------|----------------------|------------------------|----------|-----|--------------|
| Calendar Events | ✅ Yes | ✅ Yes | High | 6 hours | 1 hour (Smart) |
| Calendar Connection | ✅ Yes | ✅ Yes | High | 12 hours | - |
| User Timezone | ✅ Yes | ✅ Yes | High | 7 days | - |
| Conversational History | ❌ No | ✅ Yes | Medium | 2 hours | 1 hour |
| Lead Data | ❌ No | ✅ Yes | Low | 30 minutes | 1 hour |
| Analytics Data | ✅ Yes | ✅ Yes | High | 1 hour | 1 hour |
| Battlegrounds State | ✅ Yes | ✅ Yes | Medium | 30 days | Auto-Save: 60s + Manual |

### Battlegrounds Persistent Save System

The NeuroV CRM implements a sophisticated persistent save system for the battlegrounds page, providing user-specific workflow state persistence with manual save control and comprehensive data management.

#### Key Features
- **User-Specific Storage**: Each authenticated user has isolated battlegrounds state using Supabase user ID
- **Dual Save System**: Manual save button + automatic background saving every 60 seconds
- **Activity-Aware Auto-Save**: Pauses during user interactions (dragging nodes, creating connections)
- **Comprehensive State**: Persists nodes, connections, canvas position, zoom level, and metadata
- **Differentiated Feedback**: Prominent manual save toasts vs subtle auto-save notifications
- **Long-Term Storage**: 30-day TTL for workflow configurations and designs
- **Conflict Prevention**: Auto-save never interferes with manual save operations

#### Data Persistence Scope
```typescript
interface BattlegroundsState {
  nodes: Node[]           // All workflow nodes with positions and configurations
  connections: Connection[] // Node relationships and data flow connections
  canvasPosition: Position // Current canvas pan position
  zoomLevel: number       // Current zoom level
  metadata: {
    version: string       // Schema version (1.0.0)
    timestamp: number     // Last save timestamp
    userId: string        // Supabase user ID for isolation
    deviceId: string      // Cross-device tracking
  }
}
```

#### Implementation Architecture
```typescript
// Manual save with user feedback
const { saveBattlegrounds, isLoading } = useBattlegroundsPersistence()

const handleSave = async () => {
  try {
    await saveBattlegrounds(currentState)
    toast({ title: "Success", description: "Battlegrounds saved successfully" })
  } catch (error) {
    toast({ title: "Error", description: "Failed to save battlegrounds" })
  }
}
```

#### Auto-Save Implementation
```typescript
// Activity-aware auto-save with 60-second intervals
useEffect(() => {
  if (!isAutoSaveEnabled) return

  const interval = setInterval(() => {
    // Only auto-save when:
    // 1. There are unsaved changes
    // 2. User is not actively interacting
    // 3. No manual save in progress
    if (hasUnsavedChanges && !isUserActive && !isSaving) {
      performAutoSave()
    }
  }, 60000) // 60 seconds

  return () => clearInterval(interval)
}, [isAutoSaveEnabled, hasUnsavedChanges, isUserActive, isSaving])

// Activity detection with timeout
const setUserActivity = useCallback((isDragging: boolean, isCreatingConnection: boolean) => {
  const isActive = isDragging || isCreatingConnection
  setIsUserActive(isActive)

  // 3-second timeout after activity stops
  if (isActive) {
    setLastActivityTime(Date.now())
  }
}, [])
```

#### Visual Feedback Differentiation
- **Manual Save**: Prominent toast with success/error states, 4-second duration
- **Auto-Save**: Subtle gray toast, 2-second duration, non-intrusive positioning
- **Activity Indicators**: Auto-save status available for optional UI integration

#### Integration with Existing Infrastructure
- **PersistentStorage**: Uses existing IndexedDB + localStorage fallback system
- **User Authentication**: Integrates with Supabase authentication for data isolation
- **Error Handling**: Follows established error handling and fallback patterns
- **Cache Management**: Participates in overall cache size management and cleanup

### Edge Runtime Compatibility

The NeuroV CRM implements specific patterns to ensure browser-specific caching code is compatible with Next.js Edge Runtime environments, preventing module loading conflicts.

#### The Challenge
Next.js middleware runs in Edge Runtime, which has restricted access to browser APIs. Module-level instantiation of classes that use browser APIs (IndexedDB, localStorage, window object) causes runtime errors during middleware execution.

#### Solution: Lazy Instantiation Pattern
```typescript
// ❌ Problematic: Module-level instantiation
const persistentStorage = new PersistentStorage() // Executes during import

// ✅ Solution: Function-level instantiation
function getPersistentStorage(): PersistentStorage | null {
  if (typeof window === 'undefined') {
    return null // Server-side safety
  }
  return new PersistentStorage() // Only create when needed
}
```

#### Environment Guards
```typescript
// Browser API access with environment detection
export const createIndexedDBPersister = () => {
  // Guard against server-side execution
  if (typeof window === 'undefined') {
    return null
  }

  const storage = getPersistentStorage()
  if (!storage) {
    return null
  }

  return createAsyncStoragePersister({
    storage: {
      getItem: async (key: string) => await storage.getItem(key),
      setItem: async (key: string, value: any) => await storage.setItem(key, value),
      removeItem: async (key: string) => await storage.removeItem(key)
    }
  })
}
```

#### Implementation Benefits
- **Edge Runtime Compatibility**: Prevents module loading conflicts in middleware
- **Graceful Degradation**: Safely handles server-side rendering scenarios
- **Lazy Loading**: Browser APIs only accessed when actually needed
- **Error Prevention**: Eliminates "Cannot redefine property" errors in Edge Runtime

### Security Considerations

#### Sensitive Data Handling
- **Production**: Sensitive data (conversations, lead details) is NOT persisted
- **Development**: All data can be persisted for testing purposes
- **Encryption**: Future enhancement for encrypted storage of sensitive data

#### Data Classification
```typescript
const SENSITIVE_DATA_PATTERNS = [
  'conversation',
  'lead-data',
  'phone-number',
  'personal-info'
]
```

#### SSR-Safe localStorage Patterns
```typescript
// SSR-safe localStorage access with fallbacks
function getOrCreateDeviceId(): string {
  // SSR safety check
  if (typeof window === 'undefined') {
    return `device-ssr-fallback-${Date.now()}`
  }

  try {
    const existing = localStorage.getItem('neurov-device-id')
    if (existing) return existing

    const newDeviceId = `device-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    localStorage.setItem('neurov-device-id', newDeviceId)
    return newDeviceId
  } catch (error) {
    // Fallback if localStorage is not available
    return `device-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
```

### Cross-Device Synchronization

**Note: Cross-device cache synchronization has been removed in favor of the current TanStack React Query caching strategy with 1-hour stale time and intelligent polling, which provides optimal performance without the complexity of Redis-based sync.**

The current caching approach uses:
- TanStack React Query with 1-hour standardized refresh intervals
- 30-second polling for Trust Hub review statuses ("in-review", "pending-review")
- Intelligent session-based refresh detection
- Cross-session persistence via IndexedDB/localStorage

### Error Handling & Fallbacks

#### Storage Fallback Chain
1. **IndexedDB** (Primary)
2. **localStorage** (Fallback)
3. **Memory Only** (Last resort)

#### Error Types
- `INDEXEDDB_NOT_AVAILABLE` - Browser doesn't support IndexedDB
- `STORAGE_QUOTA_EXCEEDED` - Storage limit reached
- `PERSISTENCE_FAILED` - General persistence failure
- `SYNC_FAILED` - Cross-device sync failure

#### Automatic Recovery
```typescript
// Automatic quota management
if (error.type === CacheErrorType.STORAGE_QUOTA_EXCEEDED) {
  await cleanupOldCacheEntries()
  await retryOperation()
}
```

## Performance Optimizations

### Intelligent Caching
- **Selective Persistence**: Only essential data is persisted
- **Priority-Based Caching**: High-priority data gets preferential treatment
- **Automatic Cleanup**: Old cache entries are automatically removed

### Cache Size Management
- **Maximum Size**: 50MB per user
- **Cleanup Threshold**: 80% capacity triggers cleanup
- **Retention Policy**: Data older than 24 hours is eligible for removal

### Performance Monitoring
```typescript
// Real-time performance metrics
const metrics = {
  successRate: 95, // Percentage of successful operations
  averageDuration: 150, // Average operation time in ms
  cacheHitRate: 85, // Percentage of cache hits
  totalOperations: 1250 // Total operations performed
}
```

## Usage Examples

### Basic Cache-Aware Query
```typescript
import { useCacheAwareQuery } from '@/hooks/use-enhanced-cache'

export function useCalendarEvents(startDate: Date, endDate: Date) {
  return useCacheAwareQuery(
    ['calendar-events', startDate.toISOString(), endDate.toISOString()],
    () => fetchCalendarEvents(startDate, endDate),
    {
      staleTime: 5 * 60 * 1000,
      persist: true,
      sensitive: false,
      priority: 'high'
    }
  )
}
```

### Cache-Aware Mutation with Invalidation
```typescript
import { useCacheAwareMutation } from '@/hooks/use-enhanced-cache'

export function useUpdateLeadStatus() {
  return useCacheAwareMutation(
    ({ phoneNumber, status }) => updateLeadStatus(phoneNumber, status),
    {
      relatedDataTypes: ['lead', 'conversation'],
      invalidateQueries: [
        ['leads'],
        ['conversational-history']
      ]
    }
  )
}
```

### Manual Cache Management
```typescript
import { useEnhancedCache } from '@/hooks/use-enhanced-cache'

export function CacheManagementComponent() {
  const { refreshCache, invalidateRelatedData, cacheStats } = useEnhancedCache()

  const handleRefresh = async () => {
    await refreshCache({ 
      bypassCache: true, 
      reason: 'manual_refresh' 
    })
  }

  const handleDataUpdate = async () => {
    await invalidateRelatedData('calendar')
  }

  return (
    <div>
      <p>Cache Hit Rate: {cacheStats.cacheHitRate}%</p>
      <button onClick={handleRefresh}>Refresh Cache</button>
      <button onClick={handleDataUpdate}>Invalidate Calendar</button>
    </div>
  )
}
```

### Battlegrounds Persistent Save
```typescript
import { useBattlegroundsPersistence } from '@/hooks/use-battlegrounds-persistence'

export function BattlegroundsPage() {
  const {
    nodes,
    connections,
    canvasPosition,
    zoomLevel,
    saveBattlegrounds,
    isLoading,
    hasUnsavedChanges,
    isAutoSaveEnabled,
    lastAutoSaveTime,
    isUserActive,
    setUserActivity,
    enableAutoSave,
    disableAutoSave
  } = useBattlegroundsPersistence()

  // Track user activity for auto-save pausing
  useEffect(() => {
    const isDragging = dragState.isDragging || dragState.hasMovedThreshold
    const isCreatingConnection = connectionCreation.isCreating
    setUserActivity(isDragging, isCreatingConnection)
  }, [dragState.isDragging, dragState.hasMovedThreshold, connectionCreation.isCreating, setUserActivity])

  const handleManualSave = async () => {
    try {
      await saveBattlegrounds()
      // Manual save shows prominent success toast
    } catch (error) {
      // Manual save shows prominent error toast
    }
  }

  return (
    <div>
      <SaveButton
        onSave={handleManualSave}
        disabled={isLoading || !hasUnsavedChanges}
      />

      {/* Auto-save status indicator (optional) */}
      {isAutoSaveEnabled && (
        <div className="text-xs text-gray-500">
          Auto-save: {isUserActive ? 'Paused (active)' : 'Enabled'}
          {lastAutoSaveTime && ` • Last: ${formatTime(lastAutoSaveTime)}`}
        </div>
      )}

      {/* Battlegrounds canvas and controls */}
    </div>
  )
}
```

## Testing & Validation

### Automated Testing
- **Cross-Session Persistence**: Validates data survives browser restarts
- **Storage Fallbacks**: Tests IndexedDB → localStorage fallback
- **Security Compliance**: Ensures sensitive data handling
- **Performance Benchmarks**: Measures cache operation performance

### Test Script Usage
```bash
# Run cache persistence tests
node frontend/scripts/test-cache-persistence.js

# Or in browser console
const test = new CachePersistenceTest()
await test.runAllTests()
```

### Performance Monitoring
```typescript
import { CachePerformanceMonitor } from '@/components/cache/cache-performance-monitor'

// Compact view for status bar
<CachePerformanceMonitor showDetails={false} />

// Detailed view for admin panel
<CachePerformanceMonitor showDetails={true} />
```

## Configuration

### Environment Variables
```env
# Cache configuration
NEXT_PUBLIC_CACHE_BUSTER=1.0.0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### Query Client Configuration
```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 30 * 60 * 1000, // 30 minutes
      refetchOnWindowFocus: false,
      retry: 3
    }
  }
})
```

## Migration & Versioning

### Cache Versioning
- **Current Version**: 1.0.0
- **Migration Strategy**: Automatic migration for compatible versions
- **Breaking Changes**: Full cache clear for incompatible versions

### Data Migration
```typescript
private migrateCache(data: any, fromVersion: string): any | null {
  if (fromVersion === '0.9.0' && CACHE_VERSION === '1.0.0') {
    // Perform migration logic
    return migratedData
  }
  return data
}
```

## Troubleshooting

### Common Issues

1. **Cache Not Persisting**
   - Check browser storage permissions
   - Verify IndexedDB support
   - Check storage quota

2. **Cross-Device Sync Not Working**
   - Verify Redis connection
   - Check authentication tokens
   - Validate network connectivity

3. **Performance Issues**
   - Monitor cache size
   - Check cleanup frequency
   - Analyze query patterns

### Debug Tools
```typescript
// Access cache statistics
window.__CACHE_TEST_RESULTS__

// Monitor cache health
const { healthMonitor } = getCacheErrorHandling()
console.log(healthMonitor.getHealthStats())
```

## Future Enhancements

### Planned Features
1. **Encrypted Storage** for sensitive data
2. **CDN Integration** for global performance
3. **Advanced Analytics** for cache optimization
4. **Real-time Sync** via WebSockets
5. **Offline Mode** with conflict resolution

### Performance Goals
- **Cache Hit Rate**: >90%
- **Operation Time**: <100ms average
- **Storage Efficiency**: <10MB per user
- **Sync Latency**: <2 seconds cross-device

---

This caching architecture provides a robust foundation for the NeuroV CRM's performance and user experience requirements while maintaining security and scalability.
